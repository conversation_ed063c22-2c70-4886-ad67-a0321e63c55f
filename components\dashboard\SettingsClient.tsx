"use client";

import { useState, useEffect } from "react";
import {
  IconSettings,
  IconUser,
  IconBell,
  IconLock,
  IconPalette,
  IconLanguage,
  IconDeviceFloppy,
  IconCheck,
  IconTrash,
  IconAlertTriangle,
  IconCreditCard,
  IconCalendar,
  IconCrown,
} from "@tabler/icons-react";
import { Button } from "@/components/ui/button";
import { deleteUserAccountAndRedirect } from "@/actions/delete-user";
import { useUserSubscription } from "@/hooks/use-user-subscription";
import Link from "next/link";

interface UserSettings {
  name: string;
  email: string;
  language: string;
  theme: string;
  notifications: {
    email: boolean;
    push: boolean;
    marketing: boolean;
  };
  privacy: {
    profileVisible: boolean;
    shareReadings: boolean;
  };
}

interface SettingsClientProps {
  userEmail: string;
  userName: string;
  userImage?: string;
}

export default function SettingsClient({
  userEmail,
  userName,
  userImage,
}: SettingsClientProps) {
  const { data: subscriptionData, isLoading: subscriptionLoading } =
    useUserSubscription();

  const [settings, setSettings] = useState<UserSettings>({
    name: userName,
    email: userEmail,
    language: "ar",
    theme: "light",
    notifications: {
      email: true,
      push: true,
      marketing: false,
    },
    privacy: {
      profileVisible: true,
      shareReadings: false,
    },
  });

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{
    type: "success" | "error";
    text: string;
  } | null>(null);

  useEffect(() => {
    // Update settings when props change
    setSettings((prev) => ({
      ...prev,
      name: userName,
      email: userEmail,
    }));
  }, [userName, userEmail]);

  const handleSave = async () => {
    try {
      setSaving(true);
      setMessage(null);

      // In a real app, you would send this to an API endpoint
      await new Promise((resolve) => setTimeout(resolve, 1000));

      setMessage({ type: "success", text: "تم حفظ الإعدادات بنجاح" });
    } catch (error) {
      setMessage({ type: "error", text: "فشل في حفظ الإعدادات" });
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    if (field.includes(".")) {
      const [parent, child] = field.split(".");
      setSettings((prev) => ({
        ...prev,
        [parent]: {
          ...(prev[parent as keyof UserSettings] as any),
          [child]: value,
        },
      }));
    } else {
      setSettings((prev) => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-4 sm:p-6 space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3 space-x-reverse">
          <IconSettings className="h-8 w-8 text-amber-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              الإعدادات
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              إدارة إعدادات حسابك وتفضيلاتك
            </p>
          </div>
        </div>
        <Button
          onClick={handleSave}
          disabled={saving}
          className="bg-amber-600 hover:bg-amber-700 text-white">
          {saving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              جاري الحفظ...
            </>
          ) : (
            <>
              <IconDeviceFloppy className="h-4 w-4 mr-2" />
              حفظ التغييرات
            </>
          )}
        </Button>
      </div>

      {/* Message */}
      {message && (
        <div
          className={`p-4 rounded-lg ${
            message.type === "success"
              ? "bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 text-green-800 dark:text-green-200"
              : "bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-800 dark:text-red-200"
          }`}>
          <div className="flex items-center">
            {message.type === "success" && (
              <IconCheck className="h-5 w-5 mr-2" />
            )}
            {message.text}
          </div>
        </div>
      )}

      {/* Profile Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center space-x-3 space-x-reverse mb-6">
          <IconUser className="h-6 w-6 text-amber-600" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            معلومات الحساب
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              الاسم
            </label>
            <input
              type="text"
              value={settings.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              البريد الإلكتروني
            </label>
            <input
              type="email"
              value={settings.email}
              readOnly
              disabled
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-100 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              لا يمكن تغيير البريد الإلكتروني
            </p>
          </div>
        </div>
      </div>

      {/* Subscription Information */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center space-x-3 space-x-reverse mb-6">
          <IconCreditCard className="h-6 w-6 text-amber-600" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            معلومات الاشتراك
          </h2>
        </div>

        {subscriptionLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600"></div>
          </div>
        ) : subscriptionData ? (
          <div className="space-y-6">
            {/* Current Plan */}
            <div className="bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
              <div className="flex items-start space-x-3 space-x-reverse">
                <IconCrown className="h-6 w-6 text-amber-600 flex-shrink-0 mt-0.5" />
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {subscriptionData.currentPlan?.nameAr || "الخطة المجانية"}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    {subscriptionData.currentPlan?.descriptionAr ||
                      "خطة مجانية محدودة"}
                  </p>

                  {/* Plan Features */}
                  {subscriptionData.currentPlan?.featuresAr &&
                    subscriptionData.currentPlan.featuresAr.length > 0 && (
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                          مميزات الخطة:
                        </h4>
                        <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                          {subscriptionData.currentPlan.featuresAr.map(
                            (feature, index) => (
                              <li
                                key={index}
                                className="flex items-center space-x-2 space-x-reverse">
                                <IconCheck className="h-4 w-4 text-green-500 flex-shrink-0" />
                                <span>{feature}</span>
                              </li>
                            ),
                          )}
                        </ul>
                      </div>
                    )}

                  {/* Plan Price */}
                  {subscriptionData.currentPlan && (
                    <div className="flex items-center space-x-2 space-x-reverse text-sm">
                      <span className="font-semibold text-gray-900 dark:text-white">
                        ${subscriptionData.currentPlan.price}
                      </span>
                      <span className="text-gray-600 dark:text-gray-400">
                        /{" "}
                        {subscriptionData.currentPlan.interval === "month"
                          ? "شهرياً"
                          : "سنوياً"}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Usage Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {subscriptionData.user.readingsUsed}
                </div>
                <div className="text-sm text-blue-800 dark:text-blue-300">
                  قراءات مستخدمة
                </div>
              </div>

              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {subscriptionData.user.remainingReadings}
                </div>
                <div className="text-sm text-green-800 dark:text-green-300">
                  قراءات متبقية
                </div>
              </div>

              <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                  {subscriptionData.user.readingsLimit}
                </div>
                <div className="text-sm text-purple-800 dark:text-purple-300">
                  إجمالي القراءات
                </div>
              </div>
            </div>

            {/* Subscription Details */}
            {subscriptionData.subscription && (
              <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                  تفاصيل الاشتراك
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">
                      حالة الاشتراك:
                    </span>
                    <span
                      className={`mr-2 px-2 py-1 rounded-full text-xs font-medium ${
                        subscriptionData.subscription.status === "active"
                          ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                          : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                      }`}>
                      {subscriptionData.subscription.status === "active"
                        ? "نشط"
                        : "غير نشط"}
                    </span>
                  </div>

                  <div className="flex items-center space-x-2 space-x-reverse">
                    <IconCalendar className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-600 dark:text-gray-400">
                      ينتهي في:
                    </span>
                    <span className="text-gray-900 dark:text-white">
                      {new Date(
                        subscriptionData.subscription.currentPeriodEnd,
                      ).toLocaleDateString("ar-SA")}
                    </span>
                  </div>

                  {subscriptionData.subscription.cancelAtPeriodEnd && (
                    <div className="md:col-span-2">
                      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
                        <p className="text-sm text-yellow-800 dark:text-yellow-200">
                          ⚠️ سيتم إلغاء اشتراكك في نهاية الفترة الحالية
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Upgrade/Manage Subscription Button */}
            <div className="flex justify-center">
              <Link href="/plans">
                <Button className="bg-amber-600 hover:bg-amber-700 text-white">
                  <IconCrown className="h-4 w-4 mr-2" />
                  {subscriptionData.currentPlan
                    ? "إدارة الاشتراك"
                    : "ترقية الخطة"}
                </Button>
              </Link>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500 dark:text-gray-400">
              فشل في تحميل معلومات الاشتراك
            </p>
          </div>
        )}
      </div>

      {/* Privacy Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center space-x-3 space-x-reverse mb-6">
          <IconLock className="h-6 w-6 text-amber-600" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            الخصوصية
          </h2>
        </div>

        <div className="space-y-4">
          {/* Delete Account Section */}
          <div>
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <div className="flex items-start space-x-3 space-x-reverse">
                <IconAlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400 flex-shrink-0 mt-0.5" />
                <div className="flex-1">
                  <h3 className="text-sm font-medium text-red-800 dark:text-red-200 mb-2">
                    حذف الحساب نهائياً
                  </h3>
                  <p className="text-sm text-red-700 dark:text-red-300 mb-4">
                    سيتم حذف حسابك وجميع بياناتك نهائياً بما في ذلك:
                  </p>
                  <ul className="text-sm text-red-700 dark:text-red-300 mb-4 list-disc list-inside space-y-1">
                    <li>جميع قراءات الفنجان والمحادثات</li>
                    <li>معلومات الحساب والملف الشخصي</li>
                    <li>الاشتراكات والمدفوعات</li>
                    <li>جميع الإعدادات والتفضيلات</li>
                  </ul>
                  <p className="text-sm text-red-700 dark:text-red-300 mb-4 font-medium">
                    ⚠️ هذا الإجراء لا يمكن التراجع عنه!
                  </p>
                  <Button
                    onClick={() => {
                      if (
                        window.confirm(
                          "هل أنت متأكد من رغبتك في حذف حسابك نهائياً؟ لا يمكن التراجع عن هذا الإجراء!",
                        )
                      ) {
                        deleteUserAccountAndRedirect();
                      }
                    }}
                    variant="destructive"
                    className="bg-red-600 hover:bg-red-700 text-white">
                    <IconTrash className="h-4 w-4 mr-2" />
                    حذف الحساب نهائياً
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
