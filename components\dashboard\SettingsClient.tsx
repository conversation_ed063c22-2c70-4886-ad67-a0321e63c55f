"use client";

import { useState, useEffect } from "react";
import { 
  IconSettings, 
  Icon<PERSON>ser, 
  IconBell, 
  IconLock, 
  IconPalette,
  IconLanguage,
  IconDevice<PERSON>loppy,
  IconCheck
} from "@tabler/icons-react";
import { Button } from "@/components/ui/button";

interface UserSettings {
  name: string;
  email: string;
  language: string;
  theme: string;
  notifications: {
    email: boolean;
    push: boolean;
    marketing: boolean;
  };
  privacy: {
    profileVisible: boolean;
    shareReadings: boolean;
  };
}

interface SettingsClientProps {
  userEmail: string;
  userName: string;
  userImage?: string;
}

export default function SettingsClient({ userEmail, userName, userImage }: SettingsClientProps) {
  const [settings, setSettings] = useState<UserSettings>({
    name: userName,
    email: userEmail,
    language: "ar",
    theme: "light",
    notifications: {
      email: true,
      push: true,
      marketing: false,
    },
    privacy: {
      profileVisible: true,
      shareReadings: false,
    },
  });

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);

  useEffect(() => {
    // Update settings when props change
    setSettings(prev => ({
      ...prev,
      name: userName,
      email: userEmail,
    }));
  }, [userName, userEmail]);

  const handleSave = async () => {
    try {
      setSaving(true);
      setMessage(null);

      // In a real app, you would send this to an API endpoint
      await new Promise(resolve => setTimeout(resolve, 1000));

      setMessage({ type: 'success', text: 'تم حفظ الإعدادات بنجاح' });
    } catch (error) {
      setMessage({ type: 'error', text: 'فشل في حفظ الإعدادات' });
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setSettings(prev => ({
        ...prev,
        [parent]: {
          ...(prev[parent as keyof UserSettings] as any),
          [child]: value
        }
      }));
    } else {
      setSettings(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-4 sm:p-6 space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3 space-x-reverse">
          <IconSettings className="h-8 w-8 text-amber-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              الإعدادات
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              إدارة إعدادات حسابك وتفضيلاتك
            </p>
          </div>
        </div>
        <Button
          onClick={handleSave}
          disabled={saving}
          className="bg-amber-600 hover:bg-amber-700 text-white">
          {saving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              جاري الحفظ...
            </>
          ) : (
            <>
              <IconDeviceFloppy className="h-4 w-4 mr-2" />
              حفظ التغييرات
            </>
          )}
        </Button>
      </div>

      {/* Message */}
      {message && (
        <div className={`p-4 rounded-lg ${
          message.type === 'success' 
            ? 'bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 text-green-800 dark:text-green-200'
            : 'bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-800 dark:text-red-200'
        }`}>
          <div className="flex items-center">
            {message.type === 'success' && <IconCheck className="h-5 w-5 mr-2" />}
            {message.text}
          </div>
        </div>
      )}

      {/* Profile Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center space-x-3 space-x-reverse mb-6">
          <IconUser className="h-6 w-6 text-amber-600" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            معلومات الحساب
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              الاسم
            </label>
            <input
              type="text"
              value={settings.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              البريد الإلكتروني
            </label>
            <input
              type="email"
              value={settings.email}
              readOnly
              disabled
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-100 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              لا يمكن تغيير البريد الإلكتروني
            </p>
          </div>
        </div>
      </div>

      {/* Appearance Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center space-x-3 space-x-reverse mb-6">
          <IconPalette className="h-6 w-6 text-amber-600" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            المظهر واللغة
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              المظهر
            </label>
            <select
              value={settings.theme}
              onChange={(e) => handleInputChange('theme', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
              <option value="light">فاتح</option>
              <option value="dark">داكن</option>
              <option value="auto">تلقائي</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              اللغة
            </label>
            <select
              value={settings.language}
              onChange={(e) => handleInputChange('language', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
              <option value="ar">العربية</option>
              <option value="en">English</option>
            </select>
          </div>
        </div>
      </div>

      {/* Notification Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center space-x-3 space-x-reverse mb-6">
          <IconBell className="h-6 w-6 text-amber-600" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            الإشعارات
          </h2>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                إشعارات البريد الإلكتروني
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                تلقي إشعارات حول قراءات الفنجان الجديدة
              </p>
            </div>
            <input
              type="checkbox"
              checked={settings.notifications.email}
              onChange={(e) => handleInputChange('notifications.email', e.target.checked)}
              className="h-4 w-4 text-amber-600 focus:ring-amber-500 border-gray-300 rounded"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                الإشعارات الفورية
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                تلقي إشعارات فورية في المتصفح
              </p>
            </div>
            <input
              type="checkbox"
              checked={settings.notifications.push}
              onChange={(e) => handleInputChange('notifications.push', e.target.checked)}
              className="h-4 w-4 text-amber-600 focus:ring-amber-500 border-gray-300 rounded"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                الإشعارات التسويقية
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                تلقي إشعارات حول العروض والميزات الجديدة
              </p>
            </div>
            <input
              type="checkbox"
              checked={settings.notifications.marketing}
              onChange={(e) => handleInputChange('notifications.marketing', e.target.checked)}
              className="h-4 w-4 text-amber-600 focus:ring-amber-500 border-gray-300 rounded"
            />
          </div>
        </div>
      </div>

      {/* Privacy Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center space-x-3 space-x-reverse mb-6">
          <IconLock className="h-6 w-6 text-amber-600" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            الخصوصية
          </h2>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                إظهار الملف الشخصي
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                السماح للآخرين برؤية ملفك الشخصي
              </p>
            </div>
            <input
              type="checkbox"
              checked={settings.privacy.profileVisible}
              onChange={(e) => handleInputChange('privacy.profileVisible', e.target.checked)}
              className="h-4 w-4 text-amber-600 focus:ring-amber-500 border-gray-300 rounded"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                مشاركة القراءات
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                السماح بمشاركة قراءات الفنجان مع الآخرين
              </p>
            </div>
            <input
              type="checkbox"
              checked={settings.privacy.shareReadings}
              onChange={(e) => handleInputChange('privacy.shareReadings', e.target.checked)}
              className="h-4 w-4 text-amber-600 focus:ring-amber-500 border-gray-300 rounded"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
