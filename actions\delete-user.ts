"use server";

import { auth } from "@/auth";
import prisma from "@/lib/prisma";
import { redirect } from "next/navigation";

interface DeleteUserResult {
  success: boolean;
  error?: string;
}

export async function deleteUserAccount(): Promise<DeleteUserResult> {
  try {
    // Get current session
    const session = await auth();
    
    if (!session?.user?.id) {
      return {
        success: false,
        error: "غير مصرح لك بهذا الإجراء"
      };
    }

    const userId = session.user.id;

    // Start a transaction to ensure all deletions happen atomically
    await prisma.$transaction(async (tx) => {
      // Get user with all related data to check what needs to be cleaned up
      const user = await tx.user.findUnique({
        where: { id: userId },
        include: {
          accounts: true,
          subscriptions: true,
          conversations: true,
        }
      });

      if (!user) {
        throw new Error("المستخدم غير موجود");
      }

      // If user has active Stripe subscriptions, we should cancel them first
      // Note: In a production app, you'd want to call Stripe API to cancel subscriptions
      // For now, we'll just delete the records (Prisma cascade will handle this)
      
      // Delete the user - this will cascade delete all related records:
      // - Account records (OAuth accounts)
      // - Subscription records 
      // - Conversation records (chat history)
      await tx.user.delete({
        where: { id: userId }
      });
    });

    return {
      success: true
    };

  } catch (error) {
    console.error("Error deleting user account:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "حدث خطأ أثناء حذف الحساب"
    };
  }
}

// Action to delete user account and redirect to home page
export async function deleteUserAccountAndRedirect(): Promise<never> {
  const result = await deleteUserAccount();
  
  if (result.success) {
    // Redirect to home page after successful deletion
    redirect("/");
  } else {
    // If deletion failed, redirect back to settings with error
    // In a real app, you might want to handle this differently
    redirect("/settings?error=" + encodeURIComponent(result.error || "فشل في حذف الحساب"));
  }
}
