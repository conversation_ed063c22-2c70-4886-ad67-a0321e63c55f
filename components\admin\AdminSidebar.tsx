"use client";

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Settings,
  Users,
  CreditCard,
  BarChart3,
  Menu,
  X,
  Home,
  ChevronLeft,
  Wallet,
  UserCheck,
  MessageSquare,
} from "lucide-react";

interface SidebarItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  description?: string;
  badge?: string;
}

const sidebarItems: SidebarItem[] = [
  {
    name: "Dashboard",
    href: "/admin",
    icon: Home,
    description: "Overview & Analytics",
  },
  {
    name: "User List",
    href: "/admin/users",
    icon: Users,
    description: "Manage all users",
  },
  {
    name: "Payment List",
    href: "/admin/payments",
    icon: CreditCard,
    description: "View all transactions",
  },
  {
    name: "Conversations",
    href: "/admin/conversations",
    icon: MessageSquare,
    description: "View all chat conversations",
  },
  {
    name: "Subscriptions",
    href: "/admin/subscriptions",
    icon: Wallet,
    description: "Active subscriptions",
  },
  {
    name: "User Analytics",
    href: "/admin/analytics",
    icon: BarChart3,
    description: "User insights & metrics",
  },
  {
    name: "الإعدادات",
    href: "/admin/settings",
    icon: Settings,
    description: "إعدادات النظام",
  },
];

export default function AdminSidebar() {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobileOpen, setIsMobileOpen] = useState(false);
  const pathname = usePathname();

  const toggleCollapsed = () => {
    setIsCollapsed(!isCollapsed);
  };

  const toggleMobile = () => {
    setIsMobileOpen(!isMobileOpen);
  };

  return (
    <>
      {/* Mobile menu button */}
      <button
        onClick={toggleMobile}
        className="lg:hidden fixed top-4 left-4 z-50 p-2 rounded-md bg-white dark:bg-gray-800 shadow-lg border border-gray-200 dark:border-gray-700">
        {isMobileOpen ? (
          <X className="h-5 w-5 text-gray-600 dark:text-gray-400" />
        ) : (
          <Menu className="h-5 w-5 text-gray-600 dark:text-gray-400" />
        )}
      </button>

      {/* Mobile overlay */}
      {isMobileOpen && (
        <div
          className="lg:hidden fixed inset-0 z-40 bg-black bg-opacity-50"
          onClick={toggleMobile}
        />
      )}

      {/* Sidebar */}
      <aside
        className={`
          fixed top-0 left-0 z-40 h-full bg-gradient-to-b from-slate-900 to-slate-800 dark:from-gray-900 dark:to-gray-800 border-r border-slate-700 dark:border-gray-700 transition-all duration-300 ease-in-out shadow-xl
          ${isCollapsed ? "w-16" : "w-64"}
          ${
            isMobileOpen
              ? "translate-x-0"
              : "-translate-x-full lg:translate-x-0"
          }
        `}>
        {/* Sidebar Header */}
        <div className="flex items-center justify-between p-6 border-b border-slate-700 dark:border-gray-700">
          {!isCollapsed && (
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Settings className="h-4 w-4 text-white" />
              </div>
              <h2 className="text-lg font-bold text-white">Admin Panel</h2>
            </div>
          )}
          <button
            onClick={toggleCollapsed}
            className="hidden lg:block p-2 rounded-lg hover:bg-slate-700 dark:hover:bg-gray-700 transition-colors">
            <ChevronLeft
              className={`h-4 w-4 text-slate-400 transition-transform ${
                isCollapsed ? "rotate-180" : ""
              }`}
            />
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-1">
          {sidebarItems.map((item) => {
            const isActive = pathname === item.href;
            const Icon = item.icon;

            return (
              <Link
                key={item.href}
                href={item.href}
                onClick={() => setIsMobileOpen(false)}
                className={`
                  group flex items-center px-3 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:scale-105
                  ${
                    isActive
                      ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg"
                      : "text-slate-300 hover:bg-slate-700 hover:text-white"
                  }
                  ${isCollapsed ? "justify-center" : ""}
                `}
                title={isCollapsed ? item.name : undefined}>
                <Icon
                  className={`h-5 w-5 ${isCollapsed ? "" : "mr-3"} ${
                    isActive
                      ? "text-white"
                      : "text-slate-400 group-hover:text-white"
                  }`}
                />
                {!isCollapsed && (
                  <div className="flex-1">
                    <div className="font-semibold">{item.name}</div>
                    {item.description && (
                      <div
                        className={`text-xs mt-0.5 ${
                          isActive
                            ? "text-blue-100"
                            : "text-slate-400 group-hover:text-slate-300"
                        }`}>
                        {item.description}
                      </div>
                    )}
                  </div>
                )}
              </Link>
            );
          })}
        </nav>
      </aside>
    </>
  );
}
