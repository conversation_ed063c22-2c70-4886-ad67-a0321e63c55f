import { auth } from "@/auth";
import prisma from "@/lib/prisma";
import { stripe } from "@/lib/stripe";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's Stripe customer ID
    const user = await prisma.user.findUnique({
      where: {
        id: session.user.id,
      },
      select: {
        stripeCustomerId: true,
      },
    });

    if (!user?.stripeCustomerId) {
      console.log("No Stripe customer ID found for user:", session.user.id);
      return NextResponse.json({
        payments: [],
        message: "No payment history found",
        debug: "No Stripe customer ID",
      });
    }

    console.log(
      "Fetching invoices for Stripe customer:",
      user.stripeCustomerId,
    );

    // Fetch invoices from Stripe
    const invoices = await stripe.invoices.list({
      customer: user.stripeCustomerId,
      limit: 50, // Get last 50 invoices
      expand: ["data.subscription", "data.payment_intent"],
    });

    console.log("Total invoices found:", invoices.data.length);
    console.log(
      "Invoice statuses:",
      invoices.data.map((inv) => ({
        id: inv.id,
        status: inv.status,
        paid: inv.paid,
        amount: inv.amount_paid,
      })),
    );

    // Get all plans to match with invoices
    const plans = await prisma.plan.findMany();
    const planMap = new Map(plans.map((plan) => [plan.stripePriceId, plan]));

    // Transform invoices to payment history
    const paymentHistory = invoices.data.map((invoice) => {
      // Get the plan from the first line item
      const lineItem = invoice.lines.data[0];
      const stripePriceId = lineItem?.price?.id;
      const plan = stripePriceId ? planMap.get(stripePriceId) : null;

      return {
        id: invoice.id,
        amount: invoice.amount_paid / 100, // Convert from cents
        currency: invoice.currency.toUpperCase(),
        status: invoice.status,
        paid: invoice.paid,
        paymentDate: new Date(
          invoice.status_transitions.paid_at * 1000,
        ).toISOString(),
        createdDate: new Date(invoice.created * 1000).toISOString(),
        invoiceNumber: invoice.number,
        description: lineItem?.description || "Subscription Payment",
        plan: plan
          ? {
              id: plan.id,
              name: plan.name,
              nameAr: plan.nameAr,
              interval: plan.interval,
            }
          : null,
        subscriptionId:
          typeof invoice.subscription === "string"
            ? invoice.subscription
            : invoice.subscription?.id,
        hostedInvoiceUrl: invoice.hosted_invoice_url,
        invoicePdf: invoice.invoice_pdf,
        periodStart: lineItem?.period?.start
          ? new Date(lineItem.period.start * 1000).toISOString()
          : null,
        periodEnd: lineItem?.period?.end
          ? new Date(lineItem.period.end * 1000).toISOString()
          : null,
      };
    });

    // Filter only paid invoices and sort by payment date
    const paidPayments = paymentHistory
      .filter((payment) => payment.paid && payment.status === "paid")
      .sort(
        (a, b) =>
          new Date(b.paymentDate).getTime() - new Date(a.paymentDate).getTime(),
      );

    console.log("Paid payments found:", paidPayments.length);
    console.log(
      "All payment statuses:",
      paymentHistory.map((p) => ({ status: p.status, paid: p.paid })),
    );

    return NextResponse.json({
      payments: paidPayments,
      total: paidPayments.length,
      debug: {
        totalInvoices: invoices.data.length,
        totalPayments: paymentHistory.length,
        paidPayments: paidPayments.length,
        stripeCustomerId: user.stripeCustomerId,
      },
    });
  } catch (error) {
    console.error("Error fetching payment history:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
