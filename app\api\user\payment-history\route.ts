import { auth } from "@/auth";
import prisma from "@/lib/prisma";
import { stripe } from "@/lib/stripe";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's subscriptions
    const user = await prisma.user.findUnique({
      where: {
        id: session.user.id,
      },
      select: {
        stripeCustomerId: true,
        subscriptions: {
          select: {
            stripeSubscriptionId: true,
            status: true,
          },
        },
      },
    });

    if (!user?.subscriptions || user.subscriptions.length === 0) {
      console.log("No subscriptions found for user:", session.user.id);
      return NextResponse.json({
        payments: [],
        message: "No payment history found",
        debug: "No subscriptions found",
      });
    }

    // Get all subscription IDs
    const subscriptionIds = user.subscriptions.map(
      (sub) => sub.stripeSubscriptionId,
    );
    console.log("Found subscriptions:", subscriptionIds);

    // Fetch invoices for all subscriptions
    const allInvoices = [];
    for (const subscriptionId of subscriptionIds) {
      try {
        const invoices = await stripe.invoices.list({
          subscription: subscriptionId,
          limit: 50,
          expand: ["data.subscription", "data.payment_intent"],
        });
        allInvoices.push(...invoices.data);
        console.log(
          `Found ${invoices.data.length} invoices for subscription ${subscriptionId}`,
        );
      } catch (error) {
        console.error(
          `Error fetching invoices for subscription ${subscriptionId}:`,
          error,
        );
      }
    }

    console.log("Total invoices found:", allInvoices.length);
    console.log(
      "Invoice statuses:",
      allInvoices.map((inv) => ({
        id: inv.id,
        status: inv.status,
        paid: inv.paid,
        amount: inv.amount_paid,
      })),
    );

    // Get all plans to match with invoices
    const plans = await prisma.plan.findMany();
    const planMap = new Map(plans.map((plan) => [plan.stripePriceId, plan]));

    // Transform invoices to payment history
    const paymentHistory = allInvoices.map((invoice) => {
      // Get the plan from the first line item
      const lineItem = invoice.lines.data[0];
      const stripePriceId = lineItem?.price?.id;
      const plan = stripePriceId ? planMap.get(stripePriceId) : null;

      return {
        id: invoice.id,
        amount: invoice.amount_paid / 100, // Convert from cents
        currency: invoice.currency.toUpperCase(),
        status: invoice.status,
        paid: invoice.paid,
        paymentDate: invoice.status_transitions.paid_at
          ? new Date(invoice.status_transitions.paid_at * 1000).toISOString()
          : new Date(invoice.created * 1000).toISOString(),
        createdDate: new Date(invoice.created * 1000).toISOString(),
        invoiceNumber: invoice.number,
        description: lineItem?.description || "Subscription Payment",
        plan: plan
          ? {
              id: plan.id,
              name: plan.name,
              nameAr: plan.nameAr,
              interval: plan.interval,
            }
          : null,
        subscriptionId:
          typeof invoice.subscription === "string"
            ? invoice.subscription
            : invoice.subscription?.id,
        hostedInvoiceUrl: invoice.hosted_invoice_url,
        invoicePdf: invoice.invoice_pdf,
        periodStart: lineItem?.period?.start
          ? new Date(lineItem.period.start * 1000).toISOString()
          : null,
        periodEnd: lineItem?.period?.end
          ? new Date(lineItem.period.end * 1000).toISOString()
          : null,
      };
    });

    // Filter successful payments (paid or open status) and sort by payment date
    const successfulPayments = paymentHistory
      .filter(
        (payment) =>
          payment.paid ||
          payment.status === "paid" ||
          payment.status === "open" ||
          payment.status === "draft",
      )
      .sort(
        (a, b) =>
          new Date(b.createdDate).getTime() - new Date(a.createdDate).getTime(),
      );

    console.log("Successful payments found:", successfulPayments.length);
    console.log(
      "All payment statuses:",
      paymentHistory.map((p) => ({ status: p.status, paid: p.paid })),
    );

    return NextResponse.json({
      payments: successfulPayments,
      total: successfulPayments.length,
      debug: {
        totalInvoices: allInvoices.length,
        totalPayments: paymentHistory.length,
        successfulPayments: successfulPayments.length,
        stripeCustomerId: user.stripeCustomerId,
        subscriptionIds: subscriptionIds,
      },
    });
  } catch (error) {
    console.error("Error fetching payment history:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
