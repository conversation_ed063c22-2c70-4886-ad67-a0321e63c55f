import { useQuery } from "@tanstack/react-query";

interface UserSubscriptionData {
  user: {
    readingsUsed: number;
    readingsLimit: number;
    remainingReadings: number;
    subscriptionStatus: string;
    stripeCustomerId: string | null;
  };
  currentPlan: {
    id: string;
    name: string;
    nameAr: string;
    description: string;
    descriptionAr: string;
    price: number;
    currency: string;
    interval: string;
    readingsLimit: number;
    features: string[];
    featuresAr: string[];
  } | null;
  subscription: {
    id: string;
    stripeSubscriptionId: string;
    status: string;
    currentPeriodStart: string;
    currentPeriodEnd: string;
    cancelAtPeriodEnd: boolean;
    createdAt: string;
  } | null;
}

// Fetch user subscription data
async function fetchUserSubscription(): Promise<UserSubscriptionData> {
  const response = await fetch("/api/user/subscription");

  if (!response.ok) {
    throw new Error("Failed to fetch user subscription");
  }

  return response.json();
}

export function useUserSubscription() {
  return useQuery({
    queryKey: ["userSubscription"],
    queryFn: fetchUserSubscription,
    staleTime: 30 * 1000, // 30 seconds
    refetchOnWindowFocus: true,
  });
}
