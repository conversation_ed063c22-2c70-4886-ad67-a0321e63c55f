"use client";

import { useState, useEffect } from "react";
import { 
  Settings, 
  Save, 
  RefreshCw, 
  Database, 
  Key, 
  Mail, 
  Globe, 
  Shield,
  Palette,
  Bell,
  Server,
  Loader2
} from "lucide-react";
import { Button } from "@/components/ui/button";

interface SystemSettings {
  siteName: string;
  siteDescription: string;
  adminEmail: string;
  maintenanceMode: boolean;
  registrationEnabled: boolean;
  emailNotifications: boolean;
  maxReadingsPerUser: number;
  defaultSubscriptionDays: number;
  stripePublishableKey: string;
  openaiApiKey: string;
  databaseUrl: string;
}

export default function SettingsPage() {
  const [settings, setSettings] = useState<SystemSettings>({
    siteName: "فنجاني - قراءة الفنجان",
    siteDescription: "منصة قراءة الفنجان بالذكاء الاصطناعي",
    adminEmail: "<EMAIL>",
    maintenanceMode: false,
    registrationEnabled: true,
    emailNotifications: true,
    maxReadingsPerUser: 10,
    defaultSubscriptionDays: 30,
    stripePublishableKey: "",
    openaiApiKey: "",
    databaseUrl: "",
  });

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      // In a real app, you would fetch from an API
      // For now, we'll use environment variables and default values
      setSettings(prev => ({
        ...prev,
        stripePublishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || "",
        // Note: We don't expose sensitive keys like OpenAI API key or database URL in the frontend
      }));
    } catch (error) {
      setMessage({ type: 'error', text: 'فشل في تحميل الإعدادات' });
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setMessage(null);

      // In a real app, you would send this to an API endpoint
      // For now, we'll just simulate a save operation
      await new Promise(resolve => setTimeout(resolve, 1000));

      setMessage({ type: 'success', text: 'تم حفظ الإعدادات بنجاح' });
    } catch (error) {
      setMessage({ type: 'error', text: 'فشل في حفظ الإعدادات' });
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: keyof SystemSettings, value: string | boolean | number) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Settings className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              إعدادات النظام
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              إدارة إعدادات الموقع والنظام
            </p>
          </div>
        </div>
        <Button
          onClick={handleSave}
          disabled={saving}
          className="bg-blue-600 hover:bg-blue-700 text-white">
          {saving ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              جاري الحفظ...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              حفظ الإعدادات
            </>
          )}
        </Button>
      </div>

      {/* Message */}
      {message && (
        <div className={`p-4 rounded-lg ${
          message.type === 'success' 
            ? 'bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 text-green-800 dark:text-green-200'
            : 'bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-800 dark:text-red-200'
        }`}>
          {message.text}
        </div>
      )}

      {/* General Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center space-x-3 mb-6">
          <Globe className="h-6 w-6 text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            الإعدادات العامة
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              اسم الموقع
            </label>
            <input
              type="text"
              value={settings.siteName}
              onChange={(e) => handleInputChange('siteName', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              البريد الإلكتروني للإدارة
            </label>
            <input
              type="email"
              value={settings.adminEmail}
              onChange={(e) => handleInputChange('adminEmail', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              وصف الموقع
            </label>
            <textarea
              value={settings.siteDescription}
              onChange={(e) => handleInputChange('siteDescription', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>
      </div>

      {/* System Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center space-x-3 mb-6">
          <Server className="h-6 w-6 text-green-600" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            إعدادات النظام
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              الحد الأقصى للقراءات لكل مستخدم
            </label>
            <input
              type="number"
              value={settings.maxReadingsPerUser}
              onChange={(e) => handleInputChange('maxReadingsPerUser', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              مدة الاشتراك الافتراضية (بالأيام)
            </label>
            <input
              type="number"
              value={settings.defaultSubscriptionDays}
              onChange={(e) => handleInputChange('defaultSubscriptionDays', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="maintenanceMode"
              checked={settings.maintenanceMode}
              onChange={(e) => handleInputChange('maintenanceMode', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="maintenanceMode" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              وضع الصيانة
            </label>
          </div>

          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="registrationEnabled"
              checked={settings.registrationEnabled}
              onChange={(e) => handleInputChange('registrationEnabled', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="registrationEnabled" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              تفعيل التسجيل الجديد
            </label>
          </div>

          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="emailNotifications"
              checked={settings.emailNotifications}
              onChange={(e) => handleInputChange('emailNotifications', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="emailNotifications" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              إشعارات البريد الإلكتروني
            </label>
          </div>
        </div>
      </div>

      {/* API Keys */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center space-x-3 mb-6">
          <Key className="h-6 w-6 text-yellow-600" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            مفاتيح API
          </h2>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Stripe Publishable Key
            </label>
            <input
              type="text"
              value={settings.stripePublishableKey}
              onChange={(e) => handleInputChange('stripePublishableKey', e.target.value)}
              placeholder="pk_test_..."
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div className="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
            <div className="flex items-center">
              <Shield className="h-5 w-5 text-yellow-600 mr-2" />
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                <strong>ملاحظة أمنية:</strong> مفاتيح API الحساسة مثل OpenAI API Key و Stripe Secret Key يتم إدارتها من خلال متغيرات البيئة على الخادم ولا يتم عرضها هنا لأسباب أمنية.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
