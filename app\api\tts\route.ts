import { NextRequest, NextResponse } from "next/server";
import { openai } from "@/lib/openai";
import { auth } from "@/auth";

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { text } = await request.json();

    if (!text) {
      return NextResponse.json({ error: "Text is required" }, { status: 400 });
    }

    // Clean the text from emojis and special characters for better TTS
    const cleanText = text
      // Remove all emojis using comprehensive unicode ranges
      .replace(
        /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu,
        "",
      )
      // Remove any remaining emoji-like characters and symbols
      .replace(/[🔮✨💫☕💕❤️🌹💖🌟]/g, "")
      // Remove any malformed unicode characters (surrogate pairs)
      .replace(/[\uD800-\uDFFF]/g, "")
      // Replace multiple spaces with single space
      .replace(/\s+/g, " ")
      .trim();

    // Validate cleaned text
    if (!cleanText || cleanText.trim().length === 0) {
      return NextResponse.json(
        { error: "No valid text content after cleaning" },
        { status: 400 },
      );
    }

    // Limit text length for TTS (OpenAI has a 4096 character limit)
    const truncatedText =
      cleanText.length > 4000
        ? cleanText.substring(0, 4000) + "..."
        : cleanText;

    console.log(
      "Generating speech for text:",
      truncatedText.substring(0, 100) + "...",
    );
    console.log("Text length:", truncatedText.length);

    // Generate speech using OpenAI TTS
    const mp3 = await openai.audio.speech.create({
      model: "tts-1",
      voice: "nova", // Female voice that works well with Arabic
      input: truncatedText,
      response_format: "mp3",
    });

    // Convert the response to a buffer
    const buffer = Buffer.from(await mp3.arrayBuffer());

    // Return the audio file
    return new NextResponse(buffer, {
      headers: {
        "Content-Type": "audio/mpeg",
        "Content-Length": buffer.length.toString(),
      },
    });
  } catch (error) {
    console.error("TTS Error:", error);

    // Provide more specific error messages
    let errorMessage = "Failed to generate speech";
    let statusCode = 500;

    if (error instanceof Error) {
      if (error.message.includes("400")) {
        errorMessage = "Invalid text content for speech generation";
        statusCode = 400;
      } else if (error.message.includes("rate_limit")) {
        errorMessage = "Rate limit exceeded. Please try again later.";
        statusCode = 429;
      } else if (error.message.includes("quota")) {
        errorMessage = "Service quota exceeded. Please try again later.";
        statusCode = 503;
      }
    }

    return NextResponse.json({ error: errorMessage }, { status: statusCode });
  }
}
