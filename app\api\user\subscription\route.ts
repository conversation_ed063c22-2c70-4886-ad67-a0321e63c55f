import { auth } from "@/auth";
import prisma from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const user = await prisma.user.findUnique({
      where: {
        id: session.user.id,
      },
      select: {
        id: true,
        readingsUsed: true,
        readingsLimit: true,
        subscriptionStatus: true,
        stripeCustomerId: true,
        currentPlan: {
          select: {
            id: true,
            name: true,
            nameAr: true,
            description: true,
            descriptionAr: true,
            price: true,
            currency: true,
            interval: true,
            readingsLimit: true,
            features: true,
            featuresAr: true,
          },
        },
        subscriptions: {
          select: {
            id: true,
            stripeSubscriptionId: true,
            status: true,
            currentPeriodStart: true,
            currentPeriodEnd: true,
            cancelAtPeriodEnd: true,
            createdAt: true,
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 1, // Get the most recent subscription
        },
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    const remainingReadings = user.readingsLimit - user.readingsUsed;
    const currentSubscription = user.subscriptions[0] || null;

    return NextResponse.json({
      user: {
        readingsUsed: user.readingsUsed,
        readingsLimit: user.readingsLimit,
        remainingReadings: Math.max(0, remainingReadings),
        subscriptionStatus: user.subscriptionStatus,
        stripeCustomerId: user.stripeCustomerId,
      },
      currentPlan: user.currentPlan,
      subscription: currentSubscription,
    });
  } catch (error) {
    console.error("Error fetching user subscription:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
