import { auth } from "@/auth";
import SettingsClient from "@/components/dashboard/SettingsClient";

export default async function SettingsPage() {
  // Get user session server-side
  const session = await auth();

  // Extract user info from session
  const userEmail = session?.user?.email || "<EMAIL>";
  const userName = session?.user?.name || "المستخدم";
  const userImage = session?.user?.image || undefined;

  return (
    <SettingsClient 
      userEmail={userEmail}
      userName={userName}
      userImage={userImage}
    />
  );
}
