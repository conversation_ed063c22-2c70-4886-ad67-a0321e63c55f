import { useQuery } from "@tanstack/react-query";

interface PaymentHistoryItem {
  id: string;
  amount: number;
  currency: string;
  status: string;
  paid: boolean;
  paymentDate: string;
  createdDate: string;
  invoiceNumber: string | null;
  description: string;
  plan: {
    id: string;
    name: string;
    nameAr: string;
    interval: string;
  } | null;
  subscriptionId: string | null;
  hostedInvoiceUrl: string | null;
  invoicePdf: string | null;
  periodStart: string | null;
  periodEnd: string | null;
}

interface PaymentHistoryData {
  payments: PaymentHistoryItem[];
  total: number;
  message?: string;
  debug?: {
    totalInvoices: number;
    totalPayments: number;
    paidPayments: number;
    stripeCustomerId: string;
  };
}

// Fetch payment history
async function fetchPaymentHistory(): Promise<PaymentHistoryData> {
  const response = await fetch("/api/user/payment-history");

  if (!response.ok) {
    throw new Error("Failed to fetch payment history");
  }

  return response.json();
}

export function usePaymentHistory() {
  return useQuery({
    queryKey: ["paymentHistory"],
    queryFn: fetchPaymentHistory,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}
